import 'package:go_router/go_router.dart';
import '../pages/home_page.dart';
import '../pages/browse_products_page.dart';
import '../pages/cart_page.dart';
import '../pages/account_page.dart';
import 'app_scaffold.dart';

final GoRouter appRouter = GoRouter(
  initialLocation: '/',
  routes: [
    ShellRoute(
      builder: (context, state, child) {
        // Determine the current index based on the location
        int currentIndex = 0;
        final location = state.uri.path;

        if (location.startsWith('/browse')) {
          currentIndex = 1;
        } else if (location.startsWith('/cart')) {
          currentIndex = 2;
        } else if (location.startsWith('/account')) {
          currentIndex = 3;
        }

        return AppScaffold(currentIndex: currentIndex, child: child);
      },
      routes: [
        GoRoute(
          path: '/',
          name: 'home',
          builder: (context, state) => const HomePage(),
        ),
        GoRoute(
          path: '/browse',
          name: 'browse',
          builder: (context, state) => const BrowseProductsPage(),
        ),
        GoRoute(
          path: '/cart',
          name: 'cart',
          builder: (context, state) => const CartPage(),
        ),
        GoRoute(
          path: '/account',
          name: 'account',
          builder: (context, state) => const AccountPage(),
        ),
      ],
    ),
  ],
);
