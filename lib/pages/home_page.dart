import 'package:flutter/material.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final PageController _pageController = PageController();
  int _currentSlide = 0;

  final List<Map<String, String>> _slides = [
    {
      'title': 'Fresh Groceries Delivered',
      'subtitle': 'Get fresh produce delivered to your doorstep',
      'color': 'blue',
    },
    {
      'title': 'Quick Pickup Available',
      'subtitle': 'Order online and pickup at your convenience',
      'color': 'green',
    },
    {
      'title': 'Special Offers Daily',
      'subtitle': 'Don\'t miss out on our amazing deals',
      'color': 'orange',
    },
  ];

  @override
  void initState() {
    super.initState();
    // Auto-scroll slideshow
    Future.delayed(const Duration(seconds: 3), _autoScroll);
  }

  void _autoScroll() {
    if (mounted) {
      setState(() {
        _currentSlide = (_currentSlide + 1) % _slides.length;
      });
      _pageController.animateToPage(
        _currentSlide,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      Future.delayed(const Duration(seconds: 3), _autoScroll);
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Slideshow
            SizedBox(
              height: 200,
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentSlide = index;
                  });
                },
                itemCount: _slides.length,
                itemBuilder: (context, index) {
                  final slide = _slides[index];
                  Color backgroundColor;
                  switch (slide['color']) {
                    case 'blue':
                      backgroundColor = Colors.blue.shade100;
                      break;
                    case 'green':
                      backgroundColor = Colors.green.shade100;
                      break;
                    case 'orange':
                      backgroundColor = Colors.orange.shade100;
                      break;
                    default:
                      backgroundColor = Colors.grey.shade100;
                  }

                  return Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.shopping_bag,
                              size: 60,
                              color:
                                  slide['color'] == 'blue'
                                      ? Colors.blue
                                      : slide['color'] == 'green'
                                      ? Colors.green
                                      : Colors.orange,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              slide['title']!,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              slide['subtitle']!,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            // Slide indicators
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                _slides.length,
                (index) => Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        _currentSlide == index
                            ? Colors.blue
                            : Colors.grey.shade300,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Delivery and Pickup buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  // Delivery Button
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        // Handle delivery tap
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Delivery selected')),
                        );
                      },
                      child: Container(
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.delivery_dining,
                              size: 40,
                              color: Colors.blue,
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Delivery',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.blue,
                              ),
                            ),
                            const Text(
                              'To your door',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Pickup Button
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        // Handle pickup tap
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Pickup selected')),
                        );
                      },
                      child: Container(
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.store, size: 40, color: Colors.green),
                            const SizedBox(height: 8),
                            const Text(
                              'Pickup',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.green,
                              ),
                            ),
                            const Text(
                              'From store',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Welcome section
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  Text(
                    'Welcome to Mamash!',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Your home for amazing products',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
