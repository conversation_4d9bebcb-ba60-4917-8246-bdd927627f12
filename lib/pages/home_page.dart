import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final PageController _pageController = PageController();
  int _currentSlide = 0;
  bool _isUserInteracting = false;

  final List<Map<String, String>> _slides = [
    {
      'image':
          'https://images.unsplash.com/photo-1542838132-92c53300491e?w=800&h=400&fit=crop',
      'color': 'blue',
    },
    {
      'image':
          'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=400&fit=crop',
      'color': 'green',
    },
    {
      'image':
          'https://images.unsplash.com/photo-1534723452862-4c874018d66d?w=800&h=400&fit=crop',
      'color': 'orange',
    },
  ];

  @override
  void initState() {
    super.initState();
    // Auto-scroll slideshow
    Future.delayed(const Duration(seconds: 3), _autoScroll);
  }

  void _autoScroll() {
    if (mounted && !_isUserInteracting) {
      setState(() {
        _currentSlide = (_currentSlide + 1) % _slides.length;
      });
      _pageController.animateToPage(
        _currentSlide,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
    if (mounted) {
      Future.delayed(const Duration(seconds: 3), _autoScroll);
    }
  }

  void _onUserInteractionStart() {
    setState(() {
      _isUserInteracting = true;
    });
  }

  void _onUserInteractionEnd() {
    // Resume auto-scroll after a delay when user stops interacting
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isUserInteracting = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Slideshow
            SizedBox(
              height: 200,
              child: GestureDetector(
                onPanStart: (_) => _onUserInteractionStart(),
                onPanEnd: (_) => _onUserInteractionEnd(),
                onTapDown: (_) => _onUserInteractionStart(),
                onTapUp: (_) => _onUserInteractionEnd(),
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentSlide = index;
                    });
                  },
                  itemCount: _slides.length,
                  itemBuilder: (context, index) {
                    final slide = _slides[index];

                    return Container(
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      clipBehavior:
                          Clip.antiAlias, // Ensures the image respects the border radius
                      child: CachedNetworkImage(
                        imageUrl: slide['image']!,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                        placeholder:
                            (context, url) => const Center(
                              child: CircularProgressIndicator(),
                            ),
                        errorWidget:
                            (context, url, error) => Container(
                              color: Colors.grey[200],
                              child: const Center(
                                child: Icon(
                                  Icons.image_not_supported,
                                  size: 50,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                      ),
                    );
                  },
                ),
              ),
            ),

            // Slide indicators
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                _slides.length,
                (index) => Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        _currentSlide == index
                            ? Colors.blue
                            : Colors.grey.shade300,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Delivery and Pickup buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  // Delivery Button
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        // Handle delivery tap
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Delivery selected')),
                        );
                      },
                      child: Container(
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.delivery_dining,
                              size: 40,
                              color: Colors.blue,
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Delivery',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.blue,
                              ),
                            ),
                            const Text(
                              'To your door',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Pickup Button
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        // Handle pickup tap
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Pickup selected')),
                        );
                      },
                      child: Container(
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.store, size: 40, color: Colors.green),
                            const SizedBox(height: 8),
                            const Text(
                              'Pickup',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.green,
                              ),
                            ),
                            const Text(
                              'From store',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Welcome section
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  Text(
                    'Welcome to Mamash!',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Your home for amazing products',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
